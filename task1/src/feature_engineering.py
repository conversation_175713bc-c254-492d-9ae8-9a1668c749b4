"""
Feature engineering module for customer support ticket classification.
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.preprocessing import LabelEncoder
from textblob import TextBlob
import re
from scipy.sparse import hstack, csr_matrix
import warnings
warnings.filterwarnings('ignore')

class FeatureEngineer:
    """Class for feature engineering on ticket data."""
    
    def __init__(self, max_features=1000):
        self.max_features = max_features
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=max_features,
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.95
        )
        self.label_encoders = {}
        
    def extract_text_features(self, df):
        """
        Extract TF-IDF features from processed text.
        
        Args:
            df (pd.DataFrame): DataFrame with processed_text column
            
        Returns:
            scipy.sparse.csr_matrix: TF-IDF feature matrix
        """
        print("Extracting TF-IDF features...")
        tfidf_features = self.tfidf_vectorizer.fit_transform(df['processed_text'])
        print(f"TF-IDF feature shape: {tfidf_features.shape}")
        
        return tfidf_features
    
    def extract_additional_features(self, df):
        """
        Extract additional numerical features from ticket text.
        
        Args:
            df (pd.DataFrame): DataFrame with ticket_text column
            
        Returns:
            pd.DataFrame: Additional features
        """
        print("Extracting additional features...")
        
        features = []
        
        for text in df['ticket_text']:
            feature_dict = {}
            
            # Text length features
            feature_dict['text_length'] = len(str(text))
            feature_dict['word_count'] = len(str(text).split())
            feature_dict['sentence_count'] = len(str(text).split('.'))
            
            # Sentiment analysis
            blob = TextBlob(str(text))
            feature_dict['sentiment_polarity'] = blob.sentiment.polarity
            feature_dict['sentiment_subjectivity'] = blob.sentiment.subjectivity
            
            # Special character counts
            feature_dict['exclamation_count'] = text.count('!')
            feature_dict['question_count'] = text.count('?')
            feature_dict['uppercase_count'] = sum(1 for c in str(text) if c.isupper())
            
            # Number and date patterns
            feature_dict['number_count'] = len(re.findall(r'\d+', str(text)))
            feature_dict['order_number_present'] = 1 if re.search(r'#\d+', str(text)) else 0
            feature_dict['date_pattern_present'] = 1 if re.search(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{1,2}\s\w+', str(text)) else 0
            
            # Urgency indicators
            urgency_words = ['urgent', 'asap', 'immediately', 'emergency', 'critical']
            feature_dict['urgency_words'] = sum(1 for word in urgency_words if word in str(text).lower())
            
            # Problem indicators
            problem_words = ['broken', 'error', 'issue', 'problem', 'fail', 'not working', 'defect']
            feature_dict['problem_words'] = sum(1 for word in problem_words if word in str(text).lower())
            
            features.append(feature_dict)
        
        additional_features_df = pd.DataFrame(features)
        print(f"Additional features shape: {additional_features_df.shape}")
        
        return additional_features_df
    
    def encode_labels(self, df):
        """
        Encode categorical labels.
        
        Args:
            df (pd.DataFrame): DataFrame with label columns
            
        Returns:
            tuple: Encoded labels for issue_type and urgency_level
        """
        print("Encoding labels...")
        
        # Encode issue_type
        self.label_encoders['issue_type'] = LabelEncoder()
        y_issue = self.label_encoders['issue_type'].fit_transform(df['issue_type'])
        
        # Encode urgency_level
        self.label_encoders['urgency_level'] = LabelEncoder()
        y_urgency = self.label_encoders['urgency_level'].fit_transform(df['urgency_level'])
        
        print(f"Issue types: {list(self.label_encoders['issue_type'].classes_)}")
        print(f"Urgency levels: {list(self.label_encoders['urgency_level'].classes_)}")
        
        return y_issue, y_urgency
    
    def combine_features(self, tfidf_features, additional_features):
        """
        Combine TF-IDF and additional features.
        
        Args:
            tfidf_features: TF-IDF feature matrix
            additional_features: Additional features DataFrame
            
        Returns:
            scipy.sparse.csr_matrix: Combined feature matrix
        """
        print("Combining features...")
        
        # Convert additional features to sparse matrix
        additional_sparse = csr_matrix(additional_features.values)
        
        # Combine features
        combined_features = hstack([tfidf_features, additional_sparse])
        
        print(f"Combined features shape: {combined_features.shape}")
        
        return combined_features
    
    def create_feature_pipeline(self, df):
        """
        Complete feature engineering pipeline.
        
        Args:
            df (pd.DataFrame): Preprocessed DataFrame
            
        Returns:
            tuple: (X_features, y_issue, y_urgency, feature_names)
        """
        # Extract TF-IDF features
        tfidf_features = self.extract_text_features(df)
        
        # Extract additional features
        additional_features = self.extract_additional_features(df)
        
        # Combine features
        X_features = self.combine_features(tfidf_features, additional_features)
        
        # Encode labels
        y_issue, y_urgency = self.encode_labels(df)
        
        # Create feature names
        tfidf_feature_names = self.tfidf_vectorizer.get_feature_names_out()
        additional_feature_names = additional_features.columns.tolist()
        feature_names = list(tfidf_feature_names) + additional_feature_names
        
        return X_features, y_issue, y_urgency, feature_names

def main():
    """Test the feature engineering pipeline."""
    from data_preprocessing import DataPreprocessor
    
    # Load and preprocess data
    preprocessor = DataPreprocessor()
    df = preprocessor.load_and_preprocess_data('../data/ai_dev_assignment_tickets_complex_1000.xls')
    
    # Create features
    feature_engineer = FeatureEngineer()
    X_features, y_issue, y_urgency, feature_names = feature_engineer.create_feature_pipeline(df)
    
    print(f"\nFinal feature matrix shape: {X_features.shape}")
    print(f"Number of issue classes: {len(np.unique(y_issue))}")
    print(f"Number of urgency classes: {len(np.unique(y_urgency))}")
    
    return X_features, y_issue, y_urgency, feature_names, feature_engineer

if __name__ == "__main__":
    X_features, y_issue, y_urgency, feature_names, feature_engineer = main()

"""
Gradio web application for customer support ticket classification.
"""

import gradio as gr
import pandas as pd
import numpy as np
import joblib
import json
from scipy.sparse import hstack, csr_matrix
import warnings
warnings.filterwarnings('ignore')

# Import custom modules
from data_preprocessing import DataPreprocessor
from feature_engineering import FeatureEngineer
from entity_extraction import EntityExtractor
from models import TicketClassifier

class TicketAnalyzer:
    """Main class for ticket analysis pipeline."""
    
    def __init__(self):
        self.preprocessor = DataPreprocessor()
        self.feature_engineer = None
        self.entity_extractor = None
        self.classifier = TicketClassifier()
        self.is_loaded = False
        
    def load_models_and_components(self):
        """Load all trained models and components."""
        try:
            # Load the original dataset to get product list and fit transformers
            df = self.preprocessor.load_and_preprocess_data('../data/ai_dev_assignment_tickets_complex_1000.xls')
            
            # Initialize and fit feature engineer
            self.feature_engineer = FeatureEngineer()
            X_features, y_issue, y_urgency, feature_names = self.feature_engineer.create_feature_pipeline(df)
            
            # Initialize entity extractor with product list
            product_list = df['product'].dropna().unique().tolist()
            self.entity_extractor = EntityExtractor(product_list)
            
            # Load trained models
            self.classifier.load_models()
            
            self.is_loaded = True
            print("All models and components loaded successfully!")
            
        except Exception as e:
            print(f"Error loading models: {e}")
            # Train models if loading fails
            self.train_models()
    
    def train_models(self):
        """Train models if they don't exist."""
        print("Training models...")
        
        # Load and preprocess data
        df = self.preprocessor.load_and_preprocess_data('../data/ai_dev_assignment_tickets_complex_1000.xls')
        
        # Create features
        self.feature_engineer = FeatureEngineer()
        X_features, y_issue, y_urgency, feature_names = self.feature_engineer.create_feature_pipeline(df)
        
        # Initialize entity extractor
        product_list = df['product'].dropna().unique().tolist()
        self.entity_extractor = EntityExtractor(product_list)
        
        # Train models
        X_train, X_test, y_issue_train, y_issue_test, y_urgency_train, y_urgency_test = self.classifier.prepare_data(
            X_features, y_issue, y_urgency
        )
        
        self.classifier.train_issue_classifier(X_train, y_issue_train)
        self.classifier.train_urgency_classifier(X_train, y_urgency_train)
        
        # Save models
        self.classifier.save_models()
        
        self.is_loaded = True
        print("Models trained and saved successfully!")
    
    def process_ticket(self, ticket_text):
        """
        Process a single ticket and return predictions and entities.
        
        Args:
            ticket_text (str): Raw ticket text
            
        Returns:
            tuple: (issue_type, urgency_level, entities_json, confidence_scores)
        """
        if not self.is_loaded:
            return "Error: Models not loaded", "Error: Models not loaded", "{}", "{}"
        
        if not ticket_text.strip():
            return "Please enter ticket text", "Please enter ticket text", "{}", "{}"
        
        try:
            # Preprocess text
            processed_text = self.preprocessor.preprocess_text(ticket_text)
            
            # Extract features
            tfidf_features = self.feature_engineer.tfidf_vectorizer.transform([processed_text])
            
            # Create additional features DataFrame for single ticket
            additional_features = self.feature_engineer.extract_additional_features(
                pd.DataFrame({'ticket_text': [ticket_text]})
            )
            
            # Combine features
            combined_features = self.feature_engineer.combine_features(tfidf_features, additional_features)
            
            # Make predictions
            issue_pred, urgency_pred = self.classifier.predict(combined_features)
            
            # Get prediction probabilities
            issue_proba = self.classifier.issue_classifier.predict_proba(combined_features)[0]
            urgency_proba = self.classifier.urgency_classifier.predict_proba(combined_features)[0]
            
            # Convert predictions to labels
            issue_label = self.feature_engineer.label_encoders['issue_type'].inverse_transform([issue_pred[0]])[0]
            urgency_label = self.feature_engineer.label_encoders['urgency_level'].inverse_transform([urgency_pred[0]])[0]
            
            # Extract entities
            entities = self.entity_extractor.extract_all_entities(ticket_text)
            
            # Create confidence scores
            issue_classes = self.feature_engineer.label_encoders['issue_type'].classes_
            urgency_classes = self.feature_engineer.label_encoders['urgency_level'].classes_
            
            confidence_scores = {
                'issue_type_confidence': {
                    class_name: f"{prob:.3f}" 
                    for class_name, prob in zip(issue_classes, issue_proba)
                },
                'urgency_level_confidence': {
                    class_name: f"{prob:.3f}" 
                    for class_name, prob in zip(urgency_classes, urgency_proba)
                }
            }
            
            return (
                issue_label,
                urgency_label,
                json.dumps(entities, indent=2),
                json.dumps(confidence_scores, indent=2)
            )
            
        except Exception as e:
            error_msg = f"Error processing ticket: {str(e)}"
            return error_msg, error_msg, "{}", "{}"

# Initialize the analyzer
analyzer = TicketAnalyzer()

def predict_ticket(ticket_text):
    """Wrapper function for Gradio interface."""
    return analyzer.process_ticket(ticket_text)

def create_gradio_interface():
    """Create and configure the Gradio interface."""
    
    # Load models when creating interface
    analyzer.load_models_and_components()
    
    # Example tickets for demonstration
    examples = [
        ["Payment issue for my SmartWatch V2. I was underbilled for order #29224."],
        ["I ordered SoundWave 300 but got EcoBreeze AC instead. My order number is #36824."],
        ["Order #30903 for Vision LED TV is 13 days late. Ordered on 03 March."],
        ["Facing installation issue with PhotoSnap Cam. Setup fails at step 1."],
        ["My EcoBreeze AC is broken. It stopped working after just 7 days."],
        ["Can you tell me more about the UltraClean Vacuum warranty?"]
    ]
    
    # Create interface
    interface = gr.Interface(
        fn=predict_ticket,
        inputs=[
            gr.Textbox(
                lines=5,
                placeholder="Enter customer support ticket text here...",
                label="Ticket Text",
                info="Describe your issue, include product names, order numbers, and any relevant details."
            )
        ],
        outputs=[
            gr.Textbox(label="Predicted Issue Type", info="Classification of the main issue"),
            gr.Textbox(label="Predicted Urgency Level", info="Urgency classification (Low/Medium/High)"),
            gr.Code(label="Extracted Entities", language="json", info="Key information extracted from the ticket"),
            gr.Code(label="Confidence Scores", language="json", info="Model confidence for each prediction")
        ],
        title="🎫 Customer Support Ticket Analyzer",
        description="""
        This AI-powered tool analyzes customer support tickets to:
        - **Classify Issue Type**: Categorizes the main problem (Billing, Technical, etc.)
        - **Determine Urgency Level**: Assesses priority (Low, Medium, High)
        - **Extract Key Entities**: Finds products, dates, order numbers, and complaint keywords
        
        Simply paste your ticket text below and click Submit to get instant analysis!
        """,
        examples=examples,
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            font-family: 'Arial', sans-serif;
        }
        .gr-button-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
        }
        """,
        allow_flagging="never"
    )
    
    return interface

def main():
    """Launch the Gradio application."""
    interface = create_gradio_interface()
    
    # Launch with public sharing disabled by default
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,  # Set to True if you want public sharing
        debug=True
    )

if __name__ == "__main__":
    main()

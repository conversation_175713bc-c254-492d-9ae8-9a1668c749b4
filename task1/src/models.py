"""
Machine learning models for customer support ticket classification.
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import warnings
warnings.filterwarnings('ignore')

class TicketClassifier:
    """Class for training and evaluating ticket classification models."""
    
    def __init__(self):
        self.issue_classifier = None
        self.urgency_classifier = None
        self.scaler = StandardScaler()
        self.is_fitted = False
        
    def prepare_data(self, X, y_issue, y_urgency, test_size=0.2, random_state=42):
        """
        Split data into training and testing sets.
        
        Args:
            X: Feature matrix
            y_issue: Issue type labels
            y_urgency: Urgency level labels
            test_size: Proportion of test data
            random_state: Random seed
            
        Returns:
            tuple: Train and test splits
        """
        X_train, X_test, y_issue_train, y_issue_test, y_urgency_train, y_urgency_test = train_test_split(
            X, y_issue, y_urgency, test_size=test_size, random_state=random_state, stratify=y_issue
        )
        
        print(f"Training set size: {X_train.shape[0]}")
        print(f"Test set size: {X_test.shape[0]}")
        
        return X_train, X_test, y_issue_train, y_issue_test, y_urgency_train, y_urgency_test
    
    def train_issue_classifier(self, X_train, y_issue_train, model_type='random_forest'):
        """
        Train the issue type classifier.
        
        Args:
            X_train: Training features
            y_issue_train: Training issue labels
            model_type: Type of model to use
            
        Returns:
            Trained model
        """
        print(f"Training issue classifier using {model_type}...")
        
        if model_type == 'random_forest':
            self.issue_classifier = RandomForestClassifier(
                n_estimators=100,
                max_depth=20,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'logistic_regression':
            self.issue_classifier = LogisticRegression(
                max_iter=1000,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'svm':
            self.issue_classifier = SVC(
                kernel='rbf',
                random_state=42,
                probability=True
            )
        
        self.issue_classifier.fit(X_train, y_issue_train)
        
        # Cross-validation
        cv_scores = cross_val_score(self.issue_classifier, X_train, y_issue_train, cv=5)
        print(f"Issue classifier CV accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        
        return self.issue_classifier
    
    def train_urgency_classifier(self, X_train, y_urgency_train, model_type='random_forest'):
        """
        Train the urgency level classifier.
        
        Args:
            X_train: Training features
            y_urgency_train: Training urgency labels
            model_type: Type of model to use
            
        Returns:
            Trained model
        """
        print(f"Training urgency classifier using {model_type}...")
        
        if model_type == 'random_forest':
            self.urgency_classifier = RandomForestClassifier(
                n_estimators=100,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'logistic_regression':
            self.urgency_classifier = LogisticRegression(
                max_iter=1000,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'svm':
            self.urgency_classifier = SVC(
                kernel='rbf',
                random_state=42,
                probability=True
            )
        
        self.urgency_classifier.fit(X_train, y_urgency_train)
        
        # Cross-validation
        cv_scores = cross_val_score(self.urgency_classifier, X_train, y_urgency_train, cv=5)
        print(f"Urgency classifier CV accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        
        return self.urgency_classifier
    
    def evaluate_models(self, X_test, y_issue_test, y_urgency_test, label_encoders):
        """
        Evaluate both classifiers on test data.
        
        Args:
            X_test: Test features
            y_issue_test: Test issue labels
            y_urgency_test: Test urgency labels
            label_encoders: Dictionary of label encoders
            
        Returns:
            dict: Evaluation results
        """
        results = {}
        
        # Issue classifier evaluation
        issue_pred = self.issue_classifier.predict(X_test)
        issue_accuracy = accuracy_score(y_issue_test, issue_pred)
        
        print("Issue Type Classification Results:")
        print(f"Accuracy: {issue_accuracy:.4f}")
        print("\nClassification Report:")
        print(classification_report(
            y_issue_test, issue_pred,
            target_names=label_encoders['issue_type'].classes_
        ))
        
        # Urgency classifier evaluation
        urgency_pred = self.urgency_classifier.predict(X_test)
        urgency_accuracy = accuracy_score(y_urgency_test, urgency_pred)
        
        print("\nUrgency Level Classification Results:")
        print(f"Accuracy: {urgency_accuracy:.4f}")
        print("\nClassification Report:")
        print(classification_report(
            y_urgency_test, urgency_pred,
            target_names=label_encoders['urgency_level'].classes_
        ))
        
        results = {
            'issue_accuracy': issue_accuracy,
            'urgency_accuracy': urgency_accuracy,
            'issue_predictions': issue_pred,
            'urgency_predictions': urgency_pred,
            'issue_true': y_issue_test,
            'urgency_true': y_urgency_test
        }
        
        return results
    
    def plot_confusion_matrices(self, results, label_encoders, save_path=None):
        """
        Plot confusion matrices for both classifiers.
        
        Args:
            results: Evaluation results
            label_encoders: Dictionary of label encoders
            save_path: Path to save plots
        """
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Issue type confusion matrix
        issue_cm = confusion_matrix(results['issue_true'], results['issue_predictions'])
        sns.heatmap(
            issue_cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=label_encoders['issue_type'].classes_,
            yticklabels=label_encoders['issue_type'].classes_,
            ax=axes[0]
        )
        axes[0].set_title('Issue Type Classification\nConfusion Matrix')
        axes[0].set_xlabel('Predicted')
        axes[0].set_ylabel('Actual')
        
        # Urgency level confusion matrix
        urgency_cm = confusion_matrix(results['urgency_true'], results['urgency_predictions'])
        sns.heatmap(
            urgency_cm, annot=True, fmt='d', cmap='Reds',
            xticklabels=label_encoders['urgency_level'].classes_,
            yticklabels=label_encoders['urgency_level'].classes_,
            ax=axes[1]
        )
        axes[1].set_title('Urgency Level Classification\nConfusion Matrix')
        axes[1].set_xlabel('Predicted')
        axes[1].set_ylabel('Actual')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def save_models(self, model_path='../models/'):
        """
        Save trained models to disk.
        
        Args:
            model_path: Directory to save models
        """
        joblib.dump(self.issue_classifier, f'{model_path}issue_classifier.pkl')
        joblib.dump(self.urgency_classifier, f'{model_path}urgency_classifier.pkl')
        print(f"Models saved to {model_path}")
    
    def load_models(self, model_path='../models/'):
        """
        Load trained models from disk.
        
        Args:
            model_path: Directory containing models
        """
        self.issue_classifier = joblib.load(f'{model_path}issue_classifier.pkl')
        self.urgency_classifier = joblib.load(f'{model_path}urgency_classifier.pkl')
        self.is_fitted = True
        print(f"Models loaded from {model_path}")
    
    def predict(self, X):
        """
        Make predictions using trained models.
        
        Args:
            X: Feature matrix
            
        Returns:
            tuple: (issue_predictions, urgency_predictions)
        """
        if not self.is_fitted and (self.issue_classifier is None or self.urgency_classifier is None):
            raise ValueError("Models must be trained or loaded before making predictions")
        
        issue_pred = self.issue_classifier.predict(X)
        urgency_pred = self.urgency_classifier.predict(X)
        
        return issue_pred, urgency_pred

def main():
    """Test the model training pipeline."""
    from data_preprocessing import DataPreprocessor
    from feature_engineering import FeatureEngineer
    
    # Load and preprocess data
    preprocessor = DataPreprocessor()
    df = preprocessor.load_and_preprocess_data('../data/ai_dev_assignment_tickets_complex_1000.xls')
    
    # Create features
    feature_engineer = FeatureEngineer()
    X_features, y_issue, y_urgency, feature_names = feature_engineer.create_feature_pipeline(df)
    
    # Initialize classifier
    classifier = TicketClassifier()
    
    # Prepare data
    X_train, X_test, y_issue_train, y_issue_test, y_urgency_train, y_urgency_test = classifier.prepare_data(
        X_features, y_issue, y_urgency
    )
    
    # Train models
    classifier.train_issue_classifier(X_train, y_issue_train)
    classifier.train_urgency_classifier(X_train, y_urgency_train)
    
    # Evaluate models
    results = classifier.evaluate_models(X_test, y_issue_test, y_urgency_test, feature_engineer.label_encoders)
    
    # Plot confusion matrices
    classifier.plot_confusion_matrices(results, feature_engineer.label_encoders)
    
    # Save models
    classifier.save_models()
    
    return classifier, results

if __name__ == "__main__":
    classifier, results = main()

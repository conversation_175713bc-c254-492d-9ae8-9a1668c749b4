"""
Data preprocessing module for customer support ticket classification.
"""

import pandas as pd
import numpy as np
import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
import warnings
warnings.filterwarnings('ignore')

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

class DataPreprocessor:
    """Class for preprocessing customer support ticket data."""
    
    def __init__(self):
        self.lemmatizer = WordNetLemmatizer()
        self.stop_words = set(stopwords.words('english'))
        
    def clean_text(self, text):
        """
        Clean and normalize text data.
        
        Args:
            text (str): Raw text to clean
            
        Returns:
            str: Cleaned text
        """
        if pd.isna(text):
            return ""
        
        # Convert to string and lowercase
        text = str(text).lower()
        
        # Remove special characters but keep spaces
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def tokenize_and_lemmatize(self, text):
        """
        Tokenize text and apply lemmatization.
        
        Args:
            text (str): Text to tokenize
            
        Returns:
            list: List of lemmatized tokens
        """
        if not text:
            return []
        
        # Tokenize
        tokens = word_tokenize(text)
        
        # Remove stopwords and lemmatize
        tokens = [
            self.lemmatizer.lemmatize(token) 
            for token in tokens 
            if token not in self.stop_words and len(token) > 2
        ]
        
        return tokens
    
    def preprocess_text(self, text):
        """
        Complete text preprocessing pipeline.
        
        Args:
            text (str): Raw text
            
        Returns:
            str: Preprocessed text
        """
        # Clean text
        cleaned_text = self.clean_text(text)
        
        # Tokenize and lemmatize
        tokens = self.tokenize_and_lemmatize(cleaned_text)
        
        # Join tokens back to string
        return ' '.join(tokens)
    
    def load_and_preprocess_data(self, file_path):
        """
        Load and preprocess the ticket data.
        
        Args:
            file_path (str): Path to the Excel file
            
        Returns:
            pd.DataFrame: Preprocessed dataframe
        """
        # Load data
        df = pd.read_excel(file_path)
        
        print(f"Loaded {len(df)} tickets")
        print(f"Columns: {list(df.columns)}")
        
        # Basic data exploration
        print("\nData Info:")
        print(df.info())
        
        print("\nMissing values:")
        print(df.isnull().sum())
        
        # Handle missing values
        df['ticket_text'] = df['ticket_text'].fillna('')
        df['issue_type'] = df['issue_type'].fillna('General Inquiry')
        df['urgency_level'] = df['urgency_level'].fillna('Medium')
        df['product'] = df['product'].fillna('Unknown')
        
        # Preprocess ticket text
        print("\nPreprocessing text...")
        df['processed_text'] = df['ticket_text'].apply(self.preprocess_text)
        
        # Remove empty processed texts
        df = df[df['processed_text'].str.len() > 0].reset_index(drop=True)
        
        print(f"Final dataset size: {len(df)} tickets")
        
        # Display class distributions
        print("\nIssue Type Distribution:")
        print(df['issue_type'].value_counts())
        
        print("\nUrgency Level Distribution:")
        print(df['urgency_level'].value_counts())
        
        print("\nProduct Distribution:")
        print(df['product'].value_counts().head(10))
        
        return df

def main():
    """Test the preprocessing pipeline."""
    preprocessor = DataPreprocessor()
    
    # Test text preprocessing
    sample_text = "Payment issue for my SmartWatch V2. I was underbilled for order #29224."
    processed = preprocessor.preprocess_text(sample_text)
    print(f"Original: {sample_text}")
    print(f"Processed: {processed}")
    
    # Load and preprocess data
    df = preprocessor.load_and_preprocess_data('../data/ai_dev_assignment_tickets_complex_1000.xls')
    
    return df

if __name__ == "__main__":
    df = main()

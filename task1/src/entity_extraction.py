"""
Entity extraction module for customer support tickets.
"""

import re
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EntityExtractor:
    """Class for extracting entities from customer support tickets."""
    
    def __init__(self, product_list=None):
        self.product_list = product_list or []
        
        # Predefined complaint keywords
        self.complaint_keywords = [
            'broken', 'error', 'issue', 'problem', 'fail', 'failed', 'failure',
            'not working', 'defect', 'defective', 'malfunction', 'malfunctioning',
            'late', 'delay', 'delayed', 'wrong', 'missing', 'damaged', 'faulty',
            'bug', 'glitch', 'crash', 'freeze', 'slow', 'stuck', 'unresponsive',
            'poor', 'bad', 'terrible', 'awful', 'disappointed', 'frustrated',
            'refund', 'return', 'exchange', 'cancel', 'complaint'
        ]
        
        # Date patterns
        self.date_patterns = [
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # DD/MM/YYYY or MM/DD/YYYY
            r'\b\d{1,2}\s(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s?\d{2,4}?\b',  # DD Month YYYY
            r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{1,2},?\s?\d{2,4}?\b',  # Month DD, YYYY
            r'\b\d{1,2}\s(?:January|February|March|April|May|June|July|August|September|October|November|December)\b',  # DD Month
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s\d{1,2}\b'  # Month DD
        ]
        
        # Order number patterns
        self.order_patterns = [
            r'#\d+',  # #12345
            r'order\s*#?\s*\d+',  # order #12345 or order 12345
            r'order\s+number\s*#?\s*\d+',  # order number #12345
            r'ref\s*#?\s*\d+',  # ref #12345
            r'reference\s*#?\s*\d+'  # reference #12345
        ]
    
    def extract_products(self, text):
        """
        Extract product names from text.
        
        Args:
            text (str): Input text
            
        Returns:
            list: List of found products
        """
        if not text or not self.product_list:
            return []
        
        text_lower = text.lower()
        found_products = []
        
        for product in self.product_list:
            if product.lower() in text_lower:
                found_products.append(product)
        
        return list(set(found_products))  # Remove duplicates
    
    def extract_dates(self, text):
        """
        Extract dates from text using regex patterns.
        
        Args:
            text (str): Input text
            
        Returns:
            list: List of found dates
        """
        if not text:
            return []
        
        found_dates = []
        
        for pattern in self.date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            found_dates.extend(matches)
        
        return list(set(found_dates))  # Remove duplicates
    
    def extract_complaint_keywords(self, text):
        """
        Extract complaint-related keywords from text.
        
        Args:
            text (str): Input text
            
        Returns:
            list: List of found complaint keywords
        """
        if not text:
            return []
        
        text_lower = text.lower()
        found_keywords = []
        
        for keyword in self.complaint_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        return list(set(found_keywords))  # Remove duplicates
    
    def extract_order_numbers(self, text):
        """
        Extract order numbers from text.
        
        Args:
            text (str): Input text
            
        Returns:
            list: List of found order numbers
        """
        if not text:
            return []
        
        found_orders = []
        
        for pattern in self.order_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            found_orders.extend(matches)
        
        return list(set(found_orders))  # Remove duplicates
    
    def extract_contact_info(self, text):
        """
        Extract contact information (emails, phone numbers) from text.
        
        Args:
            text (str): Input text
            
        Returns:
            dict: Dictionary with emails and phone numbers
        """
        if not text:
            return {'emails': [], 'phone_numbers': []}
        
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        
        # Phone number patterns
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # XXX-XXX-XXXX
            r'\(\d{3}\)\s?\d{3}[-.]?\d{4}',    # (XXX) XXX-XXXX
            r'\+\d{1,3}[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}'  # International
        ]
        
        phone_numbers = []
        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            phone_numbers.extend(matches)
        
        return {
            'emails': list(set(emails)),
            'phone_numbers': list(set(phone_numbers))
        }
    
    def extract_all_entities(self, text):
        """
        Extract all entities from text.
        
        Args:
            text (str): Input text
            
        Returns:
            dict: Dictionary containing all extracted entities
        """
        entities = {
            'products': self.extract_products(text),
            'dates': self.extract_dates(text),
            'complaint_keywords': self.extract_complaint_keywords(text),
            'order_numbers': self.extract_order_numbers(text),
            'contact_info': self.extract_contact_info(text)
        }
        
        return entities
    
    def set_product_list(self, product_list):
        """
        Set the product list for extraction.
        
        Args:
            product_list (list): List of product names
        """
        self.product_list = product_list

def main():
    """Test the entity extraction pipeline."""
    # Sample product list
    product_list = [
        'SmartWatch V2', 'UltraClean Vacuum', 'SoundWave 300', 
        'PhotoSnap Cam', 'Vision LED TV', 'EcoBreeze AC'
    ]
    
    # Initialize extractor
    extractor = EntityExtractor(product_list)
    
    # Test texts
    test_texts = [
        "Payment issue for my SmartWatch V2. I was underbilled for order #29224.",
        "I ordered SoundWave 300 but got EcoBreeze AC instead. My order number is #36824.",
        "Order #30903 for Vision LED TV is 13 days late. Ordered on 03 March.",
        "My PhotoSnap Cam is broken. It stopped working after just 7 days."
    ]
    
    print("Entity Extraction Results:")
    print("=" * 50)
    
    for i, text in enumerate(test_texts, 1):
        print(f"\nTest {i}: {text}")
        entities = extractor.extract_all_entities(text)
        
        for entity_type, values in entities.items():
            if values:
                print(f"  {entity_type}: {values}")
    
    return extractor

if __name__ == "__main__":
    extractor = main()

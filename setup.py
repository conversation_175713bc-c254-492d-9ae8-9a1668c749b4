"""
Setup script for AI Assignment project.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install requirements for both tasks."""
    print("Installing requirements for Task 1...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "task1/requirements.txt"])
    
    print("Installing requirements for Task 2...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "task2/requirements.txt"])

def download_nltk_data():
    """Download required NLTK data."""
    import nltk
    print("Downloading NLTK data...")
    nltk.download('punkt')
    nltk.download('stopwords')
    nltk.download('wordnet')

def download_spacy_model():
    """Download spaCy model."""
    print("Downloading spaCy model...")
    subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])

def create_directories():
    """Create necessary directories."""
    directories = [
        "task1/models",
        "task2/models",
        "task2/models/fine_tuned_model"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

def main():
    """Main setup function."""
    print("Setting up AI Assignment project...")
    print("=" * 50)
    
    try:
        # Create directories
        create_directories()
        
        # Install requirements
        install_requirements()
        
        # Download NLTK data
        download_nltk_data()
        
        # Download spaCy model
        download_spacy_model()
        
        print("\n" + "=" * 50)
        print("Setup completed successfully!")
        print("\nNext steps:")
        print("1. For Task 1: cd task1 && jupyter notebook notebooks/task1_ticket_classification.ipynb")
        print("2. For Task 2: cd task2 && jupyter notebook notebooks/task2_rag_quotes.ipynb")
        print("3. To run Gradio app: cd task1/src && python gradio_app.py")
        print("4. To run Streamlit app: cd task2/src && streamlit run streamlit_app.py")
        
    except Exception as e:
        print(f"Error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

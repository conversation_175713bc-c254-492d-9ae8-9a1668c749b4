"""
Streamlit web application for RAG-based quote retrieval system.
"""

import streamlit as st
import pandas as pd
import numpy as np
import json
import plotly.express as px
import plotly.graph_objects as go
from typing import List, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# Import custom modules
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_preparation import QuoteDataPreparator
from rag_pipeline import QuoteRAGPipeline
from evaluation import RAGEvaluator

class QuoteRetrievalApp:
    """Streamlit app for quote retrieval system."""

    def __init__(self):
        self.rag_pipeline = None
        self.quotes_df = None
        self.is_initialized = False

    def initialize_system(self):
        """Initialize the RAG system."""
        if self.is_initialized:
            return

        with st.spinner("Initializing quote retrieval system..."):
            try:
                # Load and prepare data
                preparator = QuoteDataPreparator()
                df = preparator.load_quotes_dataset()
                self.quotes_df = preparator.clean_and_preprocess()

                # Initialize RAG pipeline
                self.rag_pipeline = QuoteRAGPipeline()
                self.rag_pipeline.build_vector_index(self.quotes_df)

                self.is_initialized = True
                st.success("System initialized successfully!")

            except Exception as e:
                st.error(f"Error initializing system: {e}")
                st.stop()

    def create_sidebar(self):
        """Create sidebar with system information and settings."""
        st.sidebar.title("🎯 Quote Retrieval System")

        if self.is_initialized:
            stats = self.rag_pipeline.get_quote_statistics()

            st.sidebar.subheader("📊 Database Statistics")
            st.sidebar.metric("Total Quotes", stats.get('total_quotes', 0))
            st.sidebar.metric("Unique Authors", stats.get('unique_authors', 0))
            st.sidebar.metric("Unique Tags", stats.get('unique_tags', 0))
            st.sidebar.metric("Avg Quote Length", f"{stats.get('avg_quote_length', 0):.0f} chars")

            st.sidebar.subheader("⚙️ Search Settings")
            top_k = st.sidebar.slider("Number of results", 1, 20, 5)
            include_response = st.sidebar.checkbox("Generate AI response", True)

            return top_k, include_response

        return 5, True

    def display_quote_card(self, quote_data: Dict[str, Any], rank: int):
        """Display a quote in a card format."""
        with st.container():
            col1, col2 = st.columns([1, 10])

            with col1:
                st.markdown(f"**#{rank}**")

            with col2:
                # Quote text
                st.markdown(f"*\"{quote_data['quote']}\"*")

                # Author and metadata
                col_author, col_score = st.columns([3, 1])
                with col_author:
                    st.markdown(f"**— {quote_data['author']}**")
                with col_score:
                    st.markdown(f"Score: {quote_data['score']:.3f}")

                # Tags
                if quote_data['tags']:
                    tags_str = " • ".join([f"`{tag}`" for tag in quote_data['tags']])
                    st.markdown(f"Tags: {tags_str}")

                st.markdown("---")

    def search_interface(self):
        """Create the main search interface."""
        st.title("🔍 Semantic Quote Search")
        st.markdown("Search for quotes using natural language queries!")

        # Get settings from sidebar
        top_k, include_response = self.create_sidebar()

        # Search input
        query = st.text_input(
            "Enter your search query:",
            placeholder="e.g., 'quotes about hope by famous authors' or 'motivational quotes for success'",
            help="Try queries like: 'Einstein quotes about imagination', 'quotes tagged with love', or 'wisdom quotes'"
        )

        # Example queries
        st.markdown("**Example queries:**")
        example_queries = [
            "quotes about hope",
            "motivational quotes for success",
            "Einstein quotes about imagination",
            "quotes tagged with love and life",
            "wisdom quotes by ancient philosophers"
        ]

        cols = st.columns(len(example_queries))
        for i, example in enumerate(example_queries):
            if cols[i].button(f"📝 {example}", key=f"example_{i}"):
                query = example
                st.rerun()

        # Search button
        if st.button("🔍 Search Quotes", type="primary") or query:
            if query:
                self.perform_search(query, top_k, include_response)
            else:
                st.warning("Please enter a search query.")

    def perform_search(self, query: str, top_k: int, include_response: bool):
        """Perform quote search and display results."""
        with st.spinner("Searching for relevant quotes..."):
            try:
                # Perform search
                result = self.rag_pipeline.query_quotes(
                    query,
                    top_k=top_k,
                    include_response=include_response
                )

                # Display results
                st.subheader(f"📚 Search Results for: *\"{query}\"*")

                if result['retrieved_quotes']:
                    # AI Response (if enabled)
                    if include_response and result['response']:
                        st.subheader("🤖 AI Summary")
                        st.markdown(result['response'])
                        st.markdown("---")

                    # Retrieved quotes
                    st.subheader(f"📖 Top {len(result['retrieved_quotes'])} Relevant Quotes")

                    for quote_data in result['retrieved_quotes']:
                        self.display_quote_card(quote_data, quote_data['rank'])

                    # Search metadata
                    with st.expander("🔍 Search Details"):
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Quotes Found", result['metadata']['num_retrieved'])
                        with col2:
                            st.metric("Top Score", f"{result['metadata']['top_score']:.3f}")
                        with col3:
                            st.metric("Model", result['metadata']['model_used'])

                        # JSON output
                        st.subheader("📄 JSON Response")
                        st.json(result)

                else:
                    st.warning("No relevant quotes found for your query. Try different keywords or phrases.")

            except Exception as e:
                st.error(f"Error performing search: {e}")

    def analytics_page(self):
        """Create analytics and visualization page."""
        st.title("📊 Quote Database Analytics")

        if not self.is_initialized:
            st.warning("Please initialize the system first.")
            return

        # Author distribution
        st.subheader("👥 Author Distribution")
        if 'author' in self.quotes_df.columns:
            author_counts = self.quotes_df['author'].value_counts().head(20)
            fig_authors = px.bar(
                x=author_counts.values,
                y=author_counts.index,
                orientation='h',
                title="Top 20 Authors by Quote Count"
            )
            fig_authors.update_layout(height=600)
            st.plotly_chart(fig_authors, use_container_width=True)

        # Quote length distribution
        st.subheader("📏 Quote Length Distribution")
        if 'quote' in self.quotes_df.columns:
            quote_lengths = self.quotes_df['quote'].str.len()
            fig_lengths = px.histogram(
                x=quote_lengths,
                nbins=50,
                title="Distribution of Quote Lengths (characters)"
            )
            st.plotly_chart(fig_lengths, use_container_width=True)

        # Tag analysis
        st.subheader("🏷️ Tag Analysis")
        if 'tags' in self.quotes_df.columns:
            all_tags = []
            for tags in self.quotes_df['tags'].dropna():
                if isinstance(tags, list):
                    all_tags.extend(tags)

            if all_tags:
                tag_counts = pd.Series(all_tags).value_counts().head(30)
                fig_tags = px.bar(
                    x=tag_counts.values,
                    y=tag_counts.index,
                    orientation='h',
                    title="Top 30 Tags"
                )
                fig_tags.update_layout(height=800)
                st.plotly_chart(fig_tags, use_container_width=True)

    def evaluation_page(self):
        """Create evaluation page."""
        st.title("🎯 System Evaluation")

        if not self.is_initialized:
            st.warning("Please initialize the system first.")
            return

        st.markdown("Evaluate the performance of the quote retrieval system.")

        # Evaluation settings
        col1, col2 = st.columns(2)
        with col1:
            num_queries = st.slider("Number of evaluation queries", 10, 100, 30)
        with col2:
            run_evaluation = st.button("🚀 Run Evaluation", type="primary")

        if run_evaluation:
            with st.spinner("Running comprehensive evaluation..."):
                try:
                    # Initialize evaluator
                    evaluator = RAGEvaluator(self.rag_pipeline)

                    # Run evaluation
                    results = evaluator.run_comprehensive_evaluation(num_queries)

                    # Display results
                    st.subheader("📈 Evaluation Results")

                    # Retrieval metrics
                    if 'retrieval_metrics' in results:
                        st.subheader("🎯 Retrieval Performance")
                        metrics_df = pd.DataFrame([results['retrieval_metrics']]).T
                        metrics_df.columns = ['Score']
                        st.dataframe(metrics_df)

                        # Visualization
                        fig_metrics = px.bar(
                            x=metrics_df.index,
                            y=metrics_df['Score'],
                            title="Retrieval Metrics"
                        )
                        st.plotly_chart(fig_metrics, use_container_width=True)

                    # Similarity metrics
                    if 'similarity_metrics' in results:
                        st.subheader("📊 Similarity Analysis")
                        sim_df = pd.DataFrame([results['similarity_metrics']]).T
                        sim_df.columns = ['Score']
                        st.dataframe(sim_df)

                    # Full results
                    with st.expander("📄 Complete Results (JSON)"):
                        st.json(results)

                except Exception as e:
                    st.error(f"Error running evaluation: {e}")

def main():
    """Main Streamlit app."""
    st.set_page_config(
        page_title="Quote Retrieval System",
        page_icon="📚",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize app
    app = QuoteRetrievalApp()

    # Navigation
    page = st.sidebar.selectbox(
        "Navigate to:",
        ["🔍 Search Quotes", "📊 Analytics", "🎯 Evaluation"]
    )

    # Initialize system
    if not app.is_initialized:
        app.initialize_system()

    # Route to appropriate page
    if page == "🔍 Search Quotes":
        app.search_interface()
    elif page == "📊 Analytics":
        app.analytics_page()
    elif page == "🎯 Evaluation":
        app.evaluation_page()

if __name__ == "__main__":
    main()

"""
RAG (Retrieval Augmented Generation) pipeline for quote retrieval system.
"""

import pandas as pd
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
import json
import openai
from typing import List, Dict, Any
import warnings
warnings.filterwarnings('ignore')

class QuoteRAGPipeline:
    """RAG pipeline for semantic quote retrieval and generation."""
    
    def __init__(self, model_path='../models/fine_tuned_model', openai_api_key=None):
        self.model_path = model_path
        self.model = None
        self.index = None
        self.quotes_df = None
        self.embeddings = None
        
        # OpenAI setup
        if openai_api_key:
            openai.api_key = openai_api_key
        self.use_openai = openai_api_key is not None
        
    def load_model(self):
        """Load the fine-tuned sentence transformer model."""
        try:
            self.model = SentenceTransformer(self.model_path)
            print(f"Fine-tuned model loaded from {self.model_path}")
        except:
            print("Could not load fine-tuned model. Loading base model...")
            self.model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        
        return self.model
    
    def build_vector_index(self, quotes_df):
        """
        Build FAISS vector index for efficient similarity search.
        
        Args:
            quotes_df (pd.DataFrame): Quotes dataset
            
        Returns:
            faiss.Index: FAISS index
        """
        if self.model is None:
            self.load_model()
        
        print("Building vector index...")
        
        # Store quotes dataframe
        self.quotes_df = quotes_df.reset_index(drop=True)
        
        # Create search texts
        search_texts = []
        for _, row in self.quotes_df.iterrows():
            search_parts = [row['quote']]
            
            if 'author' in row and pd.notna(row['author']) and row['author'] != 'Unknown':
                search_parts.append(f"by {row['author']}")
            
            if 'tags' in row and row['tags']:
                if isinstance(row['tags'], list):
                    search_parts.append(' '.join(row['tags']))
                else:
                    search_parts.append(str(row['tags']))
            
            search_texts.append(' '.join(search_parts))
        
        # Generate embeddings
        print(f"Encoding {len(search_texts)} quotes...")
        self.embeddings = self.model.encode(search_texts, show_progress_bar=True)
        
        # Build FAISS index
        dimension = self.embeddings.shape[1]
        self.index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(self.embeddings)
        self.index.add(self.embeddings.astype('float32'))
        
        print(f"Vector index built with {self.index.ntotal} quotes")
        return self.index
    
    def retrieve_quotes(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Retrieve top-k most relevant quotes for a query.
        
        Args:
            query (str): Search query
            top_k (int): Number of quotes to retrieve
            
        Returns:
            List[Dict]: Retrieved quotes with metadata
        """
        if self.model is None or self.index is None:
            raise ValueError("Model and index must be loaded first")
        
        # Encode query
        query_embedding = self.model.encode([query])
        faiss.normalize_L2(query_embedding)
        
        # Search in index
        scores, indices = self.index.search(query_embedding.astype('float32'), top_k)
        
        # Prepare results
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx < len(self.quotes_df):
                quote_data = self.quotes_df.iloc[idx]
                result = {
                    'rank': i + 1,
                    'score': float(score),
                    'quote': quote_data['quote'],
                    'author': quote_data.get('author', 'Unknown'),
                    'tags': quote_data.get('tags', []),
                    'index': int(idx)
                }
                results.append(result)
        
        return results
    
    def generate_response(self, query: str, retrieved_quotes: List[Dict[str, Any]]) -> str:
        """
        Generate a response using retrieved quotes and LLM.
        
        Args:
            query (str): Original query
            retrieved_quotes (List[Dict]): Retrieved quotes
            
        Returns:
            str: Generated response
        """
        if not self.use_openai:
            return self.generate_simple_response(query, retrieved_quotes)
        
        # Prepare context from retrieved quotes
        context_parts = []
        for quote_data in retrieved_quotes:
            context_parts.append(
                f"Quote: \"{quote_data['quote']}\"\n"
                f"Author: {quote_data['author']}\n"
                f"Tags: {', '.join(quote_data['tags']) if quote_data['tags'] else 'None'}\n"
                f"Relevance Score: {quote_data['score']:.3f}\n"
            )
        
        context = "\n".join(context_parts)
        
        # Create prompt
        prompt = f"""Based on the following quotes and their metadata, provide a comprehensive response to the user's query.

User Query: {query}

Retrieved Quotes:
{context}

Please provide a response that:
1. Directly addresses the user's query
2. References the most relevant quotes
3. Provides insights about the authors and themes
4. Maintains accuracy to the source material

Response:"""
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that provides insights about quotes, authors, and themes based on retrieved quote data."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"Error generating response with OpenAI: {e}")
            return self.generate_simple_response(query, retrieved_quotes)
    
    def generate_simple_response(self, query: str, retrieved_quotes: List[Dict[str, Any]]) -> str:
        """
        Generate a simple response without LLM.
        
        Args:
            query (str): Original query
            retrieved_quotes (List[Dict]): Retrieved quotes
            
        Returns:
            str: Simple response
        """
        if not retrieved_quotes:
            return "No relevant quotes found for your query."
        
        response_parts = [f"Found {len(retrieved_quotes)} relevant quotes for your query: '{query}'\n"]
        
        for i, quote_data in enumerate(retrieved_quotes, 1):
            response_parts.append(
                f"{i}. \"{quote_data['quote']}\" - {quote_data['author']}"
            )
            if quote_data['tags']:
                response_parts.append(f"   Tags: {', '.join(quote_data['tags'])}")
            response_parts.append(f"   Relevance: {quote_data['score']:.3f}\n")
        
        return "\n".join(response_parts)
    
    def query_quotes(self, query: str, top_k: int = 5, include_response: bool = True) -> Dict[str, Any]:
        """
        Complete pipeline: retrieve quotes and generate response.
        
        Args:
            query (str): Search query
            top_k (int): Number of quotes to retrieve
            include_response (bool): Whether to generate LLM response
            
        Returns:
            Dict: Complete results including quotes and response
        """
        # Retrieve quotes
        retrieved_quotes = self.retrieve_quotes(query, top_k)
        
        # Generate response
        response = ""
        if include_response:
            response = self.generate_response(query, retrieved_quotes)
        
        # Prepare structured output
        result = {
            'query': query,
            'retrieved_quotes': retrieved_quotes,
            'response': response,
            'metadata': {
                'num_retrieved': len(retrieved_quotes),
                'top_score': retrieved_quotes[0]['score'] if retrieved_quotes else 0,
                'model_used': 'fine_tuned' if 'fine_tuned' in self.model_path else 'base'
            }
        }
        
        return result
    
    def save_index(self, path: str = '../models/faiss_index.bin'):
        """Save FAISS index to disk."""
        if self.index is not None:
            faiss.write_index(self.index, path)
            print(f"Index saved to {path}")
    
    def load_index(self, path: str = '../models/faiss_index.bin'):
        """Load FAISS index from disk."""
        try:
            self.index = faiss.read_index(path)
            print(f"Index loaded from {path}")
        except:
            print(f"Could not load index from {path}")
    
    def get_quote_statistics(self) -> Dict[str, Any]:
        """Get statistics about the quote database."""
        if self.quotes_df is None:
            return {}
        
        stats = {
            'total_quotes': len(self.quotes_df),
            'unique_authors': self.quotes_df['author'].nunique() if 'author' in self.quotes_df.columns else 0,
            'avg_quote_length': self.quotes_df['quote'].str.len().mean() if 'quote' in self.quotes_df.columns else 0,
            'embedding_dimension': self.embeddings.shape[1] if self.embeddings is not None else 0
        }
        
        if 'tags' in self.quotes_df.columns:
            all_tags = []
            for tags in self.quotes_df['tags'].dropna():
                if isinstance(tags, list):
                    all_tags.extend(tags)
            stats['unique_tags'] = len(set(all_tags))
        
        return stats

def main():
    """Test the RAG pipeline."""
    from data_preparation import QuoteDataPreparator
    
    # Load and prepare data
    preparator = QuoteDataPreparator()
    df = preparator.load_quotes_dataset()
    df_clean = preparator.clean_and_preprocess()
    
    # Initialize RAG pipeline
    rag_pipeline = QuoteRAGPipeline()
    
    # Build index
    rag_pipeline.build_vector_index(df_clean)
    
    # Test queries
    test_queries = [
        "quotes about hope",
        "motivational quotes",
        "quotes by Einstein",
        "quotes about life and love",
        "wisdom quotes"
    ]
    
    print("Testing RAG Pipeline:")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        result = rag_pipeline.query_quotes(query, top_k=3, include_response=False)
        
        print(f"Retrieved {result['metadata']['num_retrieved']} quotes:")
        for quote_data in result['retrieved_quotes']:
            print(f"  - \"{quote_data['quote'][:100]}...\" - {quote_data['author']} (Score: {quote_data['score']:.3f})")
    
    # Get statistics
    stats = rag_pipeline.get_quote_statistics()
    print(f"\nDatabase Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    return rag_pipeline

if __name__ == "__main__":
    rag_pipeline = main()

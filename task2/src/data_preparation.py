"""
Data preparation module for RAG-based quote retrieval system.
"""

import pandas as pd
import numpy as np
from datasets import load_dataset
import re
import warnings
warnings.filterwarnings('ignore')

class QuoteDataPreparator:
    """Class for preparing the English quotes dataset."""
    
    def __init__(self):
        self.dataset = None
        self.df = None
        
    def load_quotes_dataset(self):
        """
        Load the Abirate/english_quotes dataset from HuggingFace.
        
        Returns:
            pd.DataFrame: Processed quotes dataframe
        """
        print("Loading Abirate/english_quotes dataset...")
        
        try:
            # Load dataset from HuggingFace
            self.dataset = load_dataset("Abirate/english_quotes")
            
            # Convert to pandas DataFrame
            self.df = pd.DataFrame(self.dataset['train'])
            
            print(f"Loaded {len(self.df)} quotes")
            print(f"Columns: {list(self.df.columns)}")
            
            return self.df
            
        except Exception as e:
            print(f"Error loading dataset: {e}")
            # Create sample data for testing if dataset loading fails
            return self.create_sample_data()
    
    def create_sample_data(self):
        """
        Create sample quote data for testing purposes.
        
        Returns:
            pd.DataFrame: Sample quotes dataframe
        """
        print("Creating sample quote data...")
        
        sample_quotes = [
            {
                'quote': 'The only way to do great work is to love what you do.',
                'author': 'Steve Jobs',
                'tags': ['work', 'passion', 'success']
            },
            {
                'quote': 'Life is what happens to you while you\'re busy making other plans.',
                'author': 'John Lennon',
                'tags': ['life', 'planning', 'wisdom']
            },
            {
                'quote': 'The future belongs to those who believe in the beauty of their dreams.',
                'author': 'Eleanor Roosevelt',
                'tags': ['future', 'dreams', 'belief']
            },
            {
                'quote': 'It is during our darkest moments that we must focus to see the light.',
                'author': 'Aristotle',
                'tags': ['hope', 'perseverance', 'wisdom']
            },
            {
                'quote': 'The only impossible journey is the one you never begin.',
                'author': 'Tony Robbins',
                'tags': ['journey', 'beginning', 'motivation']
            }
        ]
        
        self.df = pd.DataFrame(sample_quotes)
        return self.df
    
    def explore_dataset(self):
        """
        Explore and analyze the quotes dataset.
        
        Returns:
            dict: Dataset statistics
        """
        if self.df is None:
            print("Dataset not loaded. Please load dataset first.")
            return {}
        
        print("Dataset Exploration:")
        print("=" * 50)
        
        # Basic statistics
        print(f"Total quotes: {len(self.df)}")
        print(f"Columns: {list(self.df.columns)}")
        
        # Check for missing values
        print("\nMissing values:")
        print(self.df.isnull().sum())
        
        # Author statistics
        if 'author' in self.df.columns:
            print(f"\nUnique authors: {self.df['author'].nunique()}")
            print("\nTop 10 authors by quote count:")
            print(self.df['author'].value_counts().head(10))
        
        # Quote length statistics
        if 'quote' in self.df.columns:
            quote_lengths = self.df['quote'].str.len()
            print(f"\nQuote length statistics:")
            print(f"  Mean: {quote_lengths.mean():.1f} characters")
            print(f"  Median: {quote_lengths.median():.1f} characters")
            print(f"  Min: {quote_lengths.min()} characters")
            print(f"  Max: {quote_lengths.max()} characters")
        
        # Tags analysis
        if 'tags' in self.df.columns:
            # Handle different tag formats
            all_tags = []
            for tags in self.df['tags'].dropna():
                if isinstance(tags, list):
                    all_tags.extend(tags)
                elif isinstance(tags, str):
                    # Try to parse string representation of list
                    try:
                        if tags.startswith('[') and tags.endswith(']'):
                            tags_list = eval(tags)
                            all_tags.extend(tags_list)
                        else:
                            # Split by comma if it's a comma-separated string
                            all_tags.extend([tag.strip() for tag in tags.split(',')])
                    except:
                        all_tags.append(tags)
            
            if all_tags:
                tag_counts = pd.Series(all_tags).value_counts()
                print(f"\nTotal unique tags: {len(tag_counts)}")
                print("\nTop 10 tags:")
                print(tag_counts.head(10))
        
        return {
            'total_quotes': len(self.df),
            'unique_authors': self.df['author'].nunique() if 'author' in self.df.columns else 0,
            'avg_quote_length': quote_lengths.mean() if 'quote' in self.df.columns else 0,
            'columns': list(self.df.columns)
        }
    
    def clean_and_preprocess(self):
        """
        Clean and preprocess the quotes dataset.
        
        Returns:
            pd.DataFrame: Cleaned dataset
        """
        if self.df is None:
            print("Dataset not loaded. Please load dataset first.")
            return None
        
        print("Cleaning and preprocessing dataset...")
        
        # Make a copy for processing
        df_clean = self.df.copy()
        
        # Handle missing values
        df_clean = df_clean.dropna(subset=['quote'])  # Remove quotes without text
        df_clean['author'] = df_clean['author'].fillna('Unknown')
        
        # Clean quote text
        df_clean['quote'] = df_clean['quote'].apply(self.clean_quote_text)
        
        # Standardize author names
        df_clean['author'] = df_clean['author'].apply(self.clean_author_name)
        
        # Process tags
        if 'tags' in df_clean.columns:
            df_clean['tags'] = df_clean['tags'].apply(self.process_tags)
        
        # Remove duplicates
        initial_count = len(df_clean)
        df_clean = df_clean.drop_duplicates(subset=['quote', 'author'])
        final_count = len(df_clean)
        
        print(f"Removed {initial_count - final_count} duplicate quotes")
        
        # Filter out very short or very long quotes
        quote_lengths = df_clean['quote'].str.len()
        df_clean = df_clean[
            (quote_lengths >= 10) & (quote_lengths <= 1000)
        ]
        
        print(f"Final dataset size: {len(df_clean)} quotes")
        
        self.df = df_clean
        return df_clean
    
    def clean_quote_text(self, text):
        """
        Clean individual quote text.
        
        Args:
            text (str): Raw quote text
            
        Returns:
            str: Cleaned quote text
        """
        if pd.isna(text):
            return ""
        
        # Convert to string
        text = str(text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Remove quotes at the beginning and end if they exist
        text = text.strip('"\'""''')
        
        # Fix common encoding issues
        text = text.replace(''', "'").replace(''', "'")
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace('–', '-').replace('—', '-')
        
        return text
    
    def clean_author_name(self, author):
        """
        Clean and standardize author names.
        
        Args:
            author (str): Raw author name
            
        Returns:
            str: Cleaned author name
        """
        if pd.isna(author):
            return "Unknown"
        
        # Convert to string and strip whitespace
        author = str(author).strip()
        
        # Handle empty strings
        if not author:
            return "Unknown"
        
        # Remove common prefixes/suffixes
        author = re.sub(r'^(Dr\.|Mr\.|Mrs\.|Ms\.|Prof\.)\s*', '', author)
        
        # Capitalize properly
        author = ' '.join(word.capitalize() for word in author.split())
        
        return author
    
    def process_tags(self, tags):
        """
        Process and standardize tags.
        
        Args:
            tags: Tags in various formats
            
        Returns:
            list: Processed tags list
        """
        if pd.isna(tags):
            return []
        
        processed_tags = []
        
        if isinstance(tags, list):
            processed_tags = tags
        elif isinstance(tags, str):
            try:
                # Try to parse string representation of list
                if tags.startswith('[') and tags.endswith(']'):
                    processed_tags = eval(tags)
                else:
                    # Split by comma
                    processed_tags = [tag.strip() for tag in tags.split(',')]
            except:
                processed_tags = [tags]
        
        # Clean individual tags
        cleaned_tags = []
        for tag in processed_tags:
            if isinstance(tag, str):
                tag = tag.strip().lower()
                if tag and len(tag) > 1:
                    cleaned_tags.append(tag)
        
        return cleaned_tags
    
    def create_search_corpus(self):
        """
        Create a search corpus combining quote, author, and tags.
        
        Returns:
            pd.DataFrame: Dataset with search corpus
        """
        if self.df is None:
            print("Dataset not loaded. Please load dataset first.")
            return None
        
        df_corpus = self.df.copy()
        
        # Create search text combining all fields
        search_texts = []
        for _, row in df_corpus.iterrows():
            search_parts = [row['quote']]
            
            if 'author' in row and pd.notna(row['author']):
                search_parts.append(f"by {row['author']}")
            
            if 'tags' in row and row['tags']:
                if isinstance(row['tags'], list):
                    search_parts.append(' '.join(row['tags']))
                else:
                    search_parts.append(str(row['tags']))
            
            search_texts.append(' '.join(search_parts))
        
        df_corpus['search_text'] = search_texts
        
        return df_corpus

def main():
    """Test the data preparation pipeline."""
    preparator = QuoteDataPreparator()
    
    # Load dataset
    df = preparator.load_quotes_dataset()
    
    # Explore dataset
    stats = preparator.explore_dataset()
    
    # Clean and preprocess
    df_clean = preparator.clean_and_preprocess()
    
    # Create search corpus
    df_corpus = preparator.create_search_corpus()
    
    print(f"\nFinal processed dataset shape: {df_corpus.shape}")
    print("\nSample processed data:")
    print(df_corpus[['quote', 'author', 'tags', 'search_text']].head())
    
    return preparator, df_corpus

if __name__ == "__main__":
    preparator, df_corpus = main()

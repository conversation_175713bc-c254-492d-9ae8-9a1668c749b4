"""
Model fine-tuning module for quote retrieval system.
"""

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer, InputExample, losses
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator
from torch.utils.data import DataLoader
import torch
import random
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

class QuoteEmbeddingModel:
    """Class for fine-tuning sentence embedding models for quote retrieval."""
    
    def __init__(self, base_model_name='sentence-transformers/all-MiniLM-L6-v2'):
        self.base_model_name = base_model_name
        self.model = None
        self.is_finetuned = False
        
    def load_base_model(self):
        """Load the base sentence transformer model."""
        print(f"Loading base model: {self.base_model_name}")
        self.model = SentenceTransformer(self.base_model_name)
        print(f"Model loaded. Embedding dimension: {self.model.get_sentence_embedding_dimension()}")
        return self.model
    
    def create_training_data(self, df, num_examples=1000):
        """
        Create training examples for fine-tuning.
        
        Args:
            df (pd.DataFrame): Quotes dataset
            num_examples (int): Number of training examples to create
            
        Returns:
            list: List of InputExample objects
        """
        print(f"Creating {num_examples} training examples...")
        
        training_examples = []
        
        # Create positive pairs (quote with author and tags)
        for i, row in df.sample(min(num_examples//2, len(df))).iterrows():
            quote = row['quote']
            author = row.get('author', '')
            tags = row.get('tags', [])
            
            # Create query variations
            queries = [
                f"quotes by {author}" if author and author != 'Unknown' else None,
                f"quotes about {' '.join(tags[:2])}" if tags else None,
                f"{author} quotes" if author and author != 'Unknown' else None,
                f"quotes tagged {tags[0]}" if tags else None
            ]
            
            # Filter out None queries
            queries = [q for q in queries if q]
            
            if queries:
                query = random.choice(queries)
                # High similarity for relevant quote
                training_examples.append(InputExample(texts=[query, quote], label=0.9))
        
        # Create negative pairs (unrelated quotes)
        for _ in range(num_examples//4):
            # Random pairs of quotes (should have low similarity)
            sample_quotes = df.sample(2)
            if len(sample_quotes) == 2:
                quote1 = sample_quotes.iloc[0]['quote']
                quote2 = sample_quotes.iloc[1]['quote']
                training_examples.append(InputExample(texts=[quote1, quote2], label=0.1))
        
        # Create medium similarity pairs (same author or tag)
        for _ in range(num_examples//4):
            # Find quotes by same author or with same tag
            if 'author' in df.columns:
                author_quotes = df[df['author'] != 'Unknown'].groupby('author').filter(lambda x: len(x) > 1)
                if len(author_quotes) > 1:
                    author = random.choice(author_quotes['author'].unique())
                    author_quotes_sample = author_quotes[author_quotes['author'] == author].sample(2)
                    if len(author_quotes_sample) == 2:
                        quote1 = author_quotes_sample.iloc[0]['quote']
                        quote2 = author_quotes_sample.iloc[1]['quote']
                        training_examples.append(InputExample(texts=[quote1, quote2], label=0.6))
        
        print(f"Created {len(training_examples)} training examples")
        return training_examples
    
    def create_evaluation_data(self, df, num_examples=200):
        """
        Create evaluation examples.
        
        Args:
            df (pd.DataFrame): Quotes dataset
            num_examples (int): Number of evaluation examples
            
        Returns:
            EmbeddingSimilarityEvaluator: Evaluation object
        """
        print(f"Creating {num_examples} evaluation examples...")
        
        eval_examples = []
        
        for i, row in df.sample(min(num_examples, len(df))).iterrows():
            quote = row['quote']
            author = row.get('author', '')
            
            if author and author != 'Unknown':
                query = f"quotes by {author}"
                eval_examples.append(InputExample(texts=[query, quote], label=0.8))
        
        # Create evaluation dataset
        sentences1 = [example.texts[0] for example in eval_examples]
        sentences2 = [example.texts[1] for example in eval_examples]
        scores = [example.label for example in eval_examples]
        
        evaluator = EmbeddingSimilarityEvaluator(sentences1, sentences2, scores)
        
        print(f"Created evaluation dataset with {len(eval_examples)} examples")
        return evaluator
    
    def fine_tune_model(self, training_examples, evaluator=None, epochs=1, batch_size=16):
        """
        Fine-tune the sentence transformer model.
        
        Args:
            training_examples (list): Training examples
            evaluator: Evaluation object
            epochs (int): Number of training epochs
            batch_size (int): Training batch size
            
        Returns:
            SentenceTransformer: Fine-tuned model
        """
        if self.model is None:
            self.load_base_model()
        
        print(f"Fine-tuning model for {epochs} epochs...")
        
        # Create data loader
        train_dataloader = DataLoader(training_examples, shuffle=True, batch_size=batch_size)
        
        # Define loss function
        train_loss = losses.CosineSimilarityLoss(self.model)
        
        # Fine-tune the model
        self.model.fit(
            train_objectives=[(train_dataloader, train_loss)],
            epochs=epochs,
            evaluator=evaluator,
            evaluation_steps=500,
            warmup_steps=100,
            output_path='../models/fine_tuned_model'
        )
        
        self.is_finetuned = True
        print("Fine-tuning completed!")
        
        return self.model
    
    def evaluate_model(self, df, num_test_queries=50):
        """
        Evaluate the fine-tuned model on quote retrieval tasks.
        
        Args:
            df (pd.DataFrame): Quotes dataset
            num_test_queries (int): Number of test queries
            
        Returns:
            dict: Evaluation metrics
        """
        if self.model is None:
            print("Model not loaded. Please load or fine-tune model first.")
            return {}
        
        print(f"Evaluating model with {num_test_queries} test queries...")
        
        # Create test queries
        test_results = []
        
        for i, row in df.sample(num_test_queries).iterrows():
            quote = row['quote']
            author = row.get('author', '')
            tags = row.get('tags', [])
            
            # Create query
            if author and author != 'Unknown':
                query = f"quotes by {author}"
            elif tags:
                query = f"quotes about {tags[0]}"
            else:
                continue
            
            # Encode query and all quotes
            query_embedding = self.model.encode([query])
            quote_embeddings = self.model.encode(df['quote'].tolist())
            
            # Calculate similarities
            similarities = cosine_similarity(query_embedding, quote_embeddings)[0]
            
            # Find rank of target quote
            target_idx = df.index.get_loc(i)
            target_similarity = similarities[target_idx]
            rank = np.sum(similarities > target_similarity) + 1
            
            test_results.append({
                'query': query,
                'target_quote': quote,
                'target_similarity': target_similarity,
                'rank': rank,
                'top_10': rank <= 10,
                'top_5': rank <= 5,
                'top_1': rank == 1
            })
        
        # Calculate metrics
        if test_results:
            metrics = {
                'num_queries': len(test_results),
                'avg_rank': np.mean([r['rank'] for r in test_results]),
                'top_1_accuracy': np.mean([r['top_1'] for r in test_results]),
                'top_5_accuracy': np.mean([r['top_5'] for r in test_results]),
                'top_10_accuracy': np.mean([r['top_10'] for r in test_results]),
                'avg_similarity': np.mean([r['target_similarity'] for r in test_results])
            }
            
            print("Evaluation Results:")
            print(f"  Average Rank: {metrics['avg_rank']:.2f}")
            print(f"  Top-1 Accuracy: {metrics['top_1_accuracy']:.3f}")
            print(f"  Top-5 Accuracy: {metrics['top_5_accuracy']:.3f}")
            print(f"  Top-10 Accuracy: {metrics['top_10_accuracy']:.3f}")
            print(f"  Average Similarity: {metrics['avg_similarity']:.3f}")
            
            return metrics
        
        return {}
    
    def save_model(self, path='../models/fine_tuned_model'):
        """Save the fine-tuned model."""
        if self.model is None:
            print("No model to save.")
            return
        
        self.model.save(path)
        print(f"Model saved to {path}")
    
    def load_model(self, path='../models/fine_tuned_model'):
        """Load a fine-tuned model."""
        try:
            self.model = SentenceTransformer(path)
            self.is_finetuned = True
            print(f"Fine-tuned model loaded from {path}")
        except:
            print(f"Could not load model from {path}. Loading base model instead.")
            self.load_base_model()
        
        return self.model
    
    def encode_quotes(self, quotes):
        """
        Encode quotes into embeddings.
        
        Args:
            quotes (list): List of quote texts
            
        Returns:
            np.ndarray: Quote embeddings
        """
        if self.model is None:
            print("Model not loaded. Please load model first.")
            return None
        
        print(f"Encoding {len(quotes)} quotes...")
        embeddings = self.model.encode(quotes, show_progress_bar=True)
        print(f"Embeddings shape: {embeddings.shape}")
        
        return embeddings

def main():
    """Test the model fine-tuning pipeline."""
    from data_preparation import QuoteDataPreparator
    
    # Load and prepare data
    preparator = QuoteDataPreparator()
    df = preparator.load_quotes_dataset()
    df_clean = preparator.clean_and_preprocess()
    
    # Initialize model
    embedding_model = QuoteEmbeddingModel()
    embedding_model.load_base_model()
    
    # Create training data
    training_examples = embedding_model.create_training_data(df_clean, num_examples=500)
    evaluator = embedding_model.create_evaluation_data(df_clean, num_examples=100)
    
    # Fine-tune model (reduced epochs for testing)
    embedding_model.fine_tune_model(training_examples, evaluator, epochs=1)
    
    # Evaluate model
    metrics = embedding_model.evaluate_model(df_clean, num_test_queries=20)
    
    # Save model
    embedding_model.save_model()
    
    return embedding_model, metrics

if __name__ == "__main__":
    embedding_model, metrics = main()

"""
RAG evaluation module using RAGAS framework.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any
import json
import warnings
warnings.filterwarnings('ignore')

# Try to import RAGAS components
try:
    from ragas import evaluate
    from ragas.metrics import (
        answer_relevancy,
        faithfulness,
        context_recall,
        context_precision,
        context_relevancy
    )
    from datasets import Dataset
    RAGAS_AVAILABLE = True
except ImportError:
    print("RAGAS not available. Using custom evaluation metrics.")
    RAGAS_AVAILABLE = False

class RAGEvaluator:
    """Class for evaluating RAG pipeline performance."""
    
    def __init__(self, rag_pipeline):
        self.rag_pipeline = rag_pipeline
        self.evaluation_results = {}
        
    def create_evaluation_dataset(self, num_queries: int = 50) -> List[Dict[str, Any]]:
        """
        Create evaluation dataset with ground truth.
        
        Args:
            num_queries (int): Number of evaluation queries to create
            
        Returns:
            List[Dict]: Evaluation dataset
        """
        if self.rag_pipeline.quotes_df is None:
            raise ValueError("RAG pipeline must have quotes loaded")
        
        print(f"Creating evaluation dataset with {num_queries} queries...")
        
        eval_data = []
        df = self.rag_pipeline.quotes_df
        
        # Sample quotes for evaluation
        sample_quotes = df.sample(min(num_queries, len(df)))
        
        for _, row in sample_quotes.iterrows():
            quote = row['quote']
            author = row.get('author', 'Unknown')
            tags = row.get('tags', [])
            
            # Create different types of queries
            queries = []
            
            # Author-based queries
            if author and author != 'Unknown':
                queries.extend([
                    f"quotes by {author}",
                    f"{author} quotes",
                    f"show me quotes from {author}"
                ])
            
            # Tag-based queries
            if tags:
                for tag in tags[:2]:  # Use first 2 tags
                    queries.extend([
                        f"quotes about {tag}",
                        f"{tag} quotes",
                        f"quotes tagged {tag}"
                    ])
            
            # Content-based queries
            words = quote.split()
            if len(words) > 3:
                key_words = words[:3]
                queries.append(f"quotes about {' '.join(key_words)}")
            
            # Select one query for this quote
            if queries:
                query = np.random.choice(queries)
                
                eval_data.append({
                    'query': query,
                    'ground_truth_quote': quote,
                    'ground_truth_author': author,
                    'ground_truth_tags': tags,
                    'ground_truth_index': df.index[df['quote'] == quote].tolist()[0]
                })
        
        print(f"Created {len(eval_data)} evaluation examples")
        return eval_data
    
    def evaluate_retrieval_metrics(self, eval_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Evaluate retrieval performance using custom metrics.
        
        Args:
            eval_data (List[Dict]): Evaluation dataset
            
        Returns:
            Dict[str, float]: Retrieval metrics
        """
        print("Evaluating retrieval metrics...")
        
        metrics = {
            'hit_rate_1': 0,
            'hit_rate_5': 0,
            'hit_rate_10': 0,
            'mrr': 0,  # Mean Reciprocal Rank
            'avg_precision_5': 0,
            'avg_precision_10': 0
        }
        
        reciprocal_ranks = []
        precision_5_scores = []
        precision_10_scores = []
        
        for eval_item in eval_data:
            query = eval_item['query']
            ground_truth_index = eval_item['ground_truth_index']
            
            # Retrieve quotes
            retrieved_quotes = self.rag_pipeline.retrieve_quotes(query, top_k=10)
            
            # Find rank of ground truth quote
            rank = None
            for i, quote_data in enumerate(retrieved_quotes):
                if quote_data['index'] == ground_truth_index:
                    rank = i + 1
                    break
            
            # Calculate metrics
            if rank is not None:
                # Hit rates
                if rank == 1:
                    metrics['hit_rate_1'] += 1
                if rank <= 5:
                    metrics['hit_rate_5'] += 1
                if rank <= 10:
                    metrics['hit_rate_10'] += 1
                
                # Reciprocal rank
                reciprocal_ranks.append(1.0 / rank)
                
                # Precision at k
                precision_5_scores.append(1.0 if rank <= 5 else 0.0)
                precision_10_scores.append(1.0 if rank <= 10 else 0.0)
            else:
                reciprocal_ranks.append(0.0)
                precision_5_scores.append(0.0)
                precision_10_scores.append(0.0)
        
        # Calculate final metrics
        num_queries = len(eval_data)
        metrics['hit_rate_1'] /= num_queries
        metrics['hit_rate_5'] /= num_queries
        metrics['hit_rate_10'] /= num_queries
        metrics['mrr'] = np.mean(reciprocal_ranks)
        metrics['avg_precision_5'] = np.mean(precision_5_scores)
        metrics['avg_precision_10'] = np.mean(precision_10_scores)
        
        return metrics
    
    def evaluate_with_ragas(self, eval_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Evaluate using RAGAS framework.
        
        Args:
            eval_data (List[Dict]): Evaluation dataset
            
        Returns:
            Dict[str, float]: RAGAS metrics
        """
        if not RAGAS_AVAILABLE:
            print("RAGAS not available. Skipping RAGAS evaluation.")
            return {}
        
        print("Evaluating with RAGAS framework...")
        
        # Prepare data for RAGAS
        questions = []
        answers = []
        contexts = []
        ground_truths = []
        
        for eval_item in eval_data[:20]:  # Limit for testing
            query = eval_item['query']
            ground_truth = eval_item['ground_truth_quote']
            
            # Get RAG response
            result = self.rag_pipeline.query_quotes(query, top_k=5, include_response=True)
            
            # Prepare context from retrieved quotes
            context_parts = []
            for quote_data in result['retrieved_quotes']:
                context_parts.append(
                    f"Quote: {quote_data['quote']} - Author: {quote_data['author']}"
                )
            
            questions.append(query)
            answers.append(result['response'])
            contexts.append(context_parts)
            ground_truths.append([ground_truth])
        
        # Create RAGAS dataset
        ragas_dataset = Dataset.from_dict({
            'question': questions,
            'answer': answers,
            'contexts': contexts,
            'ground_truths': ground_truths
        })
        
        try:
            # Evaluate with RAGAS
            result = evaluate(
                ragas_dataset,
                metrics=[
                    context_precision,
                    context_recall,
                    answer_relevancy,
                    faithfulness
                ]
            )
            
            return dict(result)
            
        except Exception as e:
            print(f"Error in RAGAS evaluation: {e}")
            return {}
    
    def evaluate_semantic_similarity(self, eval_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Evaluate semantic similarity between queries and retrieved quotes.
        
        Args:
            eval_data (List[Dict]): Evaluation dataset
            
        Returns:
            Dict[str, float]: Similarity metrics
        """
        print("Evaluating semantic similarity...")
        
        similarities = []
        
        for eval_item in eval_data:
            query = eval_item['query']
            
            # Retrieve quotes
            retrieved_quotes = self.rag_pipeline.retrieve_quotes(query, top_k=5)
            
            if retrieved_quotes:
                # Get average similarity of top 5 results
                avg_similarity = np.mean([quote['score'] for quote in retrieved_quotes])
                similarities.append(avg_similarity)
        
        metrics = {
            'avg_similarity': np.mean(similarities) if similarities else 0,
            'min_similarity': np.min(similarities) if similarities else 0,
            'max_similarity': np.max(similarities) if similarities else 0,
            'std_similarity': np.std(similarities) if similarities else 0
        }
        
        return metrics
    
    def run_comprehensive_evaluation(self, num_queries: int = 50) -> Dict[str, Any]:
        """
        Run comprehensive evaluation of the RAG pipeline.
        
        Args:
            num_queries (int): Number of evaluation queries
            
        Returns:
            Dict[str, Any]: Complete evaluation results
        """
        print("Running comprehensive RAG evaluation...")
        print("=" * 50)
        
        # Create evaluation dataset
        eval_data = self.create_evaluation_dataset(num_queries)
        
        # Run different evaluations
        results = {
            'retrieval_metrics': self.evaluate_retrieval_metrics(eval_data),
            'similarity_metrics': self.evaluate_semantic_similarity(eval_data),
            'evaluation_dataset_size': len(eval_data)
        }
        
        # Add RAGAS evaluation if available
        if RAGAS_AVAILABLE:
            results['ragas_metrics'] = self.evaluate_with_ragas(eval_data)
        
        # Store results
        self.evaluation_results = results
        
        # Print summary
        self.print_evaluation_summary()
        
        return results
    
    def print_evaluation_summary(self):
        """Print a summary of evaluation results."""
        if not self.evaluation_results:
            print("No evaluation results available.")
            return
        
        print("\nEvaluation Summary:")
        print("=" * 30)
        
        # Retrieval metrics
        if 'retrieval_metrics' in self.evaluation_results:
            print("\nRetrieval Metrics:")
            for metric, value in self.evaluation_results['retrieval_metrics'].items():
                print(f"  {metric}: {value:.4f}")
        
        # Similarity metrics
        if 'similarity_metrics' in self.evaluation_results:
            print("\nSimilarity Metrics:")
            for metric, value in self.evaluation_results['similarity_metrics'].items():
                print(f"  {metric}: {value:.4f}")
        
        # RAGAS metrics
        if 'ragas_metrics' in self.evaluation_results and self.evaluation_results['ragas_metrics']:
            print("\nRAGAS Metrics:")
            for metric, value in self.evaluation_results['ragas_metrics'].items():
                print(f"  {metric}: {value:.4f}")
    
    def save_evaluation_results(self, path: str = '../models/evaluation_results.json'):
        """Save evaluation results to file."""
        if self.evaluation_results:
            with open(path, 'w') as f:
                json.dump(self.evaluation_results, f, indent=2)
            print(f"Evaluation results saved to {path}")

def main():
    """Test the evaluation pipeline."""
    from data_preparation import QuoteDataPreparator
    from rag_pipeline import QuoteRAGPipeline
    
    # Load and prepare data
    preparator = QuoteDataPreparator()
    df = preparator.load_quotes_dataset()
    df_clean = preparator.clean_and_preprocess()
    
    # Initialize RAG pipeline
    rag_pipeline = QuoteRAGPipeline()
    rag_pipeline.build_vector_index(df_clean)
    
    # Initialize evaluator
    evaluator = RAGEvaluator(rag_pipeline)
    
    # Run evaluation
    results = evaluator.run_comprehensive_evaluation(num_queries=30)
    
    # Save results
    evaluator.save_evaluation_results()
    
    return evaluator, results

if __name__ == "__main__":
    evaluator, results = main()
